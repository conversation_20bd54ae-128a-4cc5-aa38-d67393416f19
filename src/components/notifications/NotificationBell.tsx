'use client'

/**
 * Notification Bell Component
 * 
 * Bell icon with notification badge that triggers the notification dropdown.
 * Integrates with the notification system to show unread count and manage state.
 * 
 * Features:
 * - Animated bell icon with hover effects
 * - Dynamic notification badge with count
 * - Dropdown toggle functionality
 * - Keyboard accessibility
 * - Mobile responsive design
 * - Loading and error states
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Bell, BellRing } from 'lucide-react'
import NotificationDropdown from './NotificationDropdown'
import { useNotifications } from '@/hooks/useNotifications'
import { useUser } from '@/lib/useUser'

interface NotificationBellProps {
  className?: string
}

/**
 * NotificationBell Component
 */
const NotificationBell: React.FC<NotificationBellProps> = ({
  className = ''
}) => {
  const { user } = useUser()
  const { unreadCount, loading } = useNotifications()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  // Don't render for unauthenticated users
  if (!user) {
    return null
  }

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  const closeDropdown = () => {
    setIsDropdownOpen(false)
  }

  return (
    <div className={`relative ${className}`}>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={toggleDropdown}
        className={`
          relative p-2 rounded-lg transition-all duration-300 group
          text-gray-300 hover:text-accent-400 hover:bg-gray-800/50
          focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900 focus:text-accent-400 focus:bg-gray-800/50
          ${isDropdownOpen ? 'text-accent-400 bg-gray-800/50' : ''}
        `}
        aria-label={`Notifications${unreadCount > 0 ? ` (${unreadCount} unread)` : ''}`}
        aria-expanded={isDropdownOpen}
        aria-haspopup="dialog"
        style={{ minHeight: '44px', minWidth: '44px' }}
      >
        {/* Bell Icon */}
        <motion.div
          animate={unreadCount > 0 ? { rotate: [0, 10, -10, 0] } : {}}
          transition={{
            duration: 0.5,
            repeat: unreadCount > 0 ? Infinity : 0,
            repeatDelay: 3
          }}
        >
          {unreadCount > 0 ? (
            <BellRing size={20} className="text-accent-400" />
          ) : (
            <Bell size={20} />
          )}
        </motion.div>

        {/* Notification Badge */}
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center transition-all duration-300"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </motion.div>
        )}

        {/* Subtle glow effect */}
        <span className="absolute inset-0 bg-accent-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>

        {/* Loading indicator */}
        {loading && (
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="absolute -top-1 -right-1 w-3 h-3 border border-accent-500 border-t-transparent rounded-full"
          />
        )}
      </motion.button>

      {/* Notification Dropdown */}
      <NotificationDropdown
        isOpen={isDropdownOpen}
        onClose={closeDropdown}
      />
    </div>
  )
}

export default NotificationBell
