'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { Menu, X, ShoppingCart, User, Gift } from 'lucide-react'
import { motion } from 'framer-motion'
import { useCartStore } from '@/store/cartStore'
import { useRewardCartSummary } from '@/store/rewardCartStore'

import { auth } from '@/lib/firebase'
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth'
import { signOutUser } from '@/lib/auth'
import { mainNavItems } from '@/lib/navigation'
import { LoginPopup } from '@/components/auth/LoginPopup'
import { Button } from '@/components/ui/button'
import NoSSR from '@/components/common/NoSSR'
import SearchBar from '@/components/search/SearchBar'
import { OptimizedUserProfileDropdown } from '@/components/profile/dropdown'
import NotificationBell from '@/components/notifications/NotificationBell'
import { AnimatedPointsDisplay } from '@/components/gamification/animations/AnimatedPointsDisplay'
import AnimatedAchievementNotification from '@/components/gamification/animations/AnimatedAchievementNotification'
import { useAccessibility } from '@/hooks/useAccessibility'
import { useAchievementNotifications } from '@/hooks/useAchievementNotifications'
import { useDeviceType } from '@/components/gamification/mobile/MobileUtils'

/**
 * Header - SSR-safe header component with login functionality
 * This version avoids hydration issues by being more conservative with dynamic IDs
 */
const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isLoginPopupOpen, setIsLoginPopupOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  
  // Simple Firebase auth state management
  const [user, setUser] = useState<FirebaseUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user)
      setLoading(false)
    })
    
    // Set a timeout to ensure loading finishes even if auth state doesn't change immediately
    const timeout = setTimeout(() => {
      setLoading(false)
    }, 500)
    
    return () => {
      unsubscribe()
      clearTimeout(timeout)
    }
  }, [])
  const cartItems = useCartStore(state => state.items)
  const { itemCount: rewardItemCount } = useRewardCartSummary()

  const router = useRouter()
  const pathname = usePathname()

  // Gamification integration (optional features)
  const { announce } = useAccessibility()
  const deviceType = useDeviceType()
  const {
    activeAchievement,
    closeAchievement
  } = useAchievementNotifications()

  // Ensure component is mounted before showing user-dependent content
  useEffect(() => {
    setIsMounted(true)
  }, [])


  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const toggleMenu = () => {
    const newState = !isMenuOpen
    setIsMenuOpen(newState)

    // Announce menu state change to screen readers
    announce(newState ? 'Mobile menu opened' : 'Mobile menu closed')
  }



  const navItems = mainNavItems

  const headerClasses = `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
    isScrolled ? 'bg-gray-950/95 backdrop-blur-sm shadow-lg' : 'bg-transparent'
  }`

  return (
    <header className={headerClasses}>
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-3 group" aria-label="Syndicaps homepage">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center space-x-3"
          >
            <img
              src="/logo.png"
              alt="Syndicaps Logo"
              className="h-8 w-auto transition-all duration-300 group-hover:brightness-110 group-hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.6)]"
            />
            <span className="text-2xl font-bold text-white transition-all duration-300 group-hover:text-accent-400 group-hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.6)] group-focus:text-accent-400 group-focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.6)]">
              Syndicaps
            </span>
          </motion.div>
        </Link>

        {/* Desktop Search Bar */}
        <div className="hidden md:flex flex-1 max-w-md mx-6">
          <SearchBar variant="header" />
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <ul className="flex space-x-8">
            {navItems.map((item) => (
              <motion.li
                key={item.name}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 * navItems.indexOf(item) }}
              >
                <Link
                  href={item.path}
                  className={`text-sm font-medium transition-all duration-300 relative group ${
                    pathname.startsWith(item.path)
                      ? 'text-accent-500 drop-shadow-[0_0_6px_rgba(139,92,246,0.5)]'
                      : 'text-gray-300 hover:text-white hover:drop-shadow-[0_0_6px_rgba(255,255,255,0.4)] focus:text-white focus:drop-shadow-[0_0_6px_rgba(255,255,255,0.4)]'
                  }`}
                >
                  <span className="relative z-10">{item.name}</span>
                  {/* Hover underline effect */}
                  <span className={`absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-accent-400 to-accent-600 transition-all duration-300 group-hover:w-full ${
                    pathname.startsWith(item.path) ? 'w-full' : ''
                  }`}></span>
                  {/* Neon glow effect on hover */}
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-md -z-10"></span>
                </Link>
              </motion.li>
            ))}
          </ul>
          
          <div className="flex items-center space-x-4">
            {/* Points Display - only show for logged in users */}
            {!isMounted || loading ? null : user && (
              <div className="flex items-center space-x-2">
                <AnimatedPointsDisplay
                  variant="header"
                  showCelebration={true}
                  showChangeIndicator={true}
                  onClick={() => router.push('/profile/points')}
                />
              </div>
            )}

            {/* Navigation Icons */}
            <div className="flex items-center space-x-4">
              {/* Regular Cart */}
              <Link
                href="/cart"
                className="text-gray-300 hover:text-accent-400 transition-all duration-300 relative group p-2 rounded-lg hover:bg-gray-800/50 focus:bg-gray-800/50 focus:text-accent-400 hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)] focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)]"
                aria-label="Shopping cart"
                style={{ minHeight: '44px', minWidth: '44px' }}
              >
                <ShoppingCart size={20} className="transition-transform duration-300 group-hover:scale-105 group-focus:scale-105" />
                {cartItems.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-accent-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-105 group-hover:bg-accent-400 group-focus:scale-105 group-focus:bg-accent-400">
                    {cartItems.length}
                  </span>
                )}
                {/* Subtle glow effect */}
                <span className="absolute inset-0 bg-accent-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
              </Link>

              {/* Reward Cart - only show for logged in users */}
              {isMounted && user && (
                <Link
                  href="/shop/reward-cart"
                  className="text-gray-300 hover:text-accent-400 transition-all duration-300 relative group p-2 rounded-lg hover:bg-gray-800/50 focus:bg-gray-800/50 focus:text-accent-400 hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)] focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)]"
                  aria-label="Reward cart"
                  style={{ minHeight: '44px', minWidth: '44px' }}
                >
                  <Gift size={20} className="transition-transform duration-300 group-hover:scale-105 group-focus:scale-105" />
                  {rewardItemCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-105 group-hover:from-purple-400 group-hover:to-pink-400 group-focus:scale-105 group-focus:from-purple-400 group-focus:to-pink-400">
                      {rewardItemCount}
                    </span>
                  )}
                  {/* Subtle glow effect */}
                  <span className="absolute inset-0 bg-accent-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
                </Link>
              )}

              {/* Notification Bell - only show for logged in users */}
              {isMounted && user && (
                <NotificationBell className="transition-all duration-300 hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)] focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.4)]" />
              )}
            </div>

            {/* Auth Section */}
            <div className="flex items-center">
              {!isMounted || loading ? (
                <div className="w-20 h-10 bg-gray-800 rounded animate-pulse" />
              ) : user ? (
                <OptimizedUserProfileDropdown />
              ) : (
                <Button
                  onClick={() => setIsLoginPopupOpen(true)}
                  className="bg-gradient-to-br from-gray-800 to-gray-900 text-white border-gray-700 hover:from-gray-700 hover:to-gray-800 hover:border-accent-500/50 hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.3)] focus:border-accent-500/50 focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.3)] transition-all duration-300 group"
                  style={{ minHeight: '44px' }}
                >
                  <span className="transition-all duration-300 group-hover:text-accent-100">Login</span>
                </Button>
              )}
            </div>
          </div>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-gray-300 hover:text-white hover:bg-gray-800/50 hover:drop-shadow-[0_0_8px_rgba(139,92,246,0.3)] focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900 focus:bg-gray-800/50 focus:drop-shadow-[0_0_8px_rgba(139,92,246,0.3)] rounded-lg p-2 transition-all duration-300 group"
          onClick={toggleMenu}
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          aria-expanded={isMenuOpen}
          style={{ minHeight: '44px', minWidth: '44px' }}
        >
          <span className="transition-transform duration-300 group-hover:scale-110 group-focus:scale-110 inline-block">
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </span>
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="md:hidden bg-gray-950/95 backdrop-blur-sm"
        >
          <nav className="container mx-auto px-4 py-4">
            {/* Mobile Search Bar */}
            <div className="mb-6">
              <SearchBar variant="header" />
            </div>

            <ul className="space-y-4">
              {navItems.map((item) => (
                <li key={item.name}>
                  <Link
                    href={item.path}
                    className={`block py-3 px-2 text-base font-medium rounded-lg transition-all duration-300 group relative ${
                      pathname.startsWith(item.path)
                        ? 'text-accent-500 bg-accent-500/10 drop-shadow-[0_0_6px_rgba(139,92,246,0.4)]'
                        : 'text-gray-300 hover:text-white hover:bg-gray-800/50 hover:drop-shadow-[0_0_6px_rgba(255,255,255,0.3)] focus:text-white focus:bg-gray-800/50 focus:drop-shadow-[0_0_6px_rgba(255,255,255,0.3)]'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                    style={{ minHeight: '44px' }}
                  >
                    <span className="relative z-10">{item.name}</span>
                    {/* Mobile hover glow effect */}
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg -z-10"></span>
                  </Link>
                </li>
              ))}
            </ul>
            
            <div className="flex flex-col space-y-3 mt-4 pt-4 border-t border-gray-800">
              {/* Mobile auth section */}
              {!isMounted || loading ? (
                <div className="w-full h-10 bg-gray-800 rounded animate-pulse" />
              ) : user ? (
                <div className="space-y-2">
                  <Link
                    href="/profile"
                    className="text-gray-300 hover:text-accent-400 hover:bg-gray-800/50 hover:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] focus:text-accent-400 focus:bg-gray-800/50 focus:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] transition-all duration-300 flex items-center py-3 px-2 rounded-lg group"
                    onClick={() => setIsMenuOpen(false)}
                    style={{ minHeight: '44px' }}
                  >
                    <User size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110 group-focus:scale-110" />
                    <span className="transition-all duration-300">Profile ({user.email?.split('@')[0]})</span>
                  </Link>

                  {/* Mobile Notification Bell */}
                  <div className="flex items-center py-3 px-2">
                    <NotificationBell className="mr-2" />
                    <span className="text-gray-300 text-sm">Notifications</span>
                  </div>
                  <button
                    onClick={async () => {
                      try {
                        await signOutUser()
                        setIsMenuOpen(false)
                        router.push('/')
                      } catch (error) {
                        console.error('Logout failed:', error)
                      }
                    }}
                    className="w-full text-left text-red-400 hover:text-red-300 hover:bg-red-900/20 transition-all duration-300 flex items-center py-3 px-2 rounded-lg group"
                    style={{ minHeight: '44px' }}
                  >
                    <User size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110 group-focus:scale-110" />
                    <span className="transition-all duration-300">Sign out</span>
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => {
                    setIsLoginPopupOpen(true)
                    setIsMenuOpen(false)
                  }}
                  className="text-gray-300 hover:text-accent-400 hover:bg-gray-800/50 hover:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] focus:text-accent-400 focus:bg-gray-800/50 focus:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] transition-all duration-300 flex items-center py-3 px-2 rounded-lg group w-full text-left"
                  style={{ minHeight: '44px' }}
                >
                  <User size={20} className="mr-2 transition-transform duration-300 group-hover:scale-110 group-focus:scale-110" />
                  <span className="transition-all duration-300">Login</span>
                </button>
              )}

              <Link
                href="/cart"
                className="text-gray-300 hover:text-accent-400 hover:bg-gray-800/50 hover:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] focus:text-accent-400 focus:bg-gray-800/50 focus:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] transition-all duration-300 flex items-center py-3 px-2 rounded-lg group"
                onClick={() => setIsMenuOpen(false)}
                style={{ minHeight: '44px' }}
              >
                <ShoppingCart size={20} className="mr-2 transition-transform duration-300 group-hover:scale-105 group-focus:scale-105" />
                <span className="transition-all duration-300">Cart ({cartItems.length})</span>
              </Link>

              {/* Mobile Reward Cart */}
              {isMounted && user && (
                <Link
                  href="/shop/reward-cart"
                  className="text-gray-300 hover:text-accent-400 hover:bg-gray-800/50 hover:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] focus:text-accent-400 focus:bg-gray-800/50 focus:drop-shadow-[0_0_6px_rgba(139,92,246,0.3)] transition-all duration-300 flex items-center py-3 px-2 rounded-lg group"
                  onClick={() => setIsMenuOpen(false)}
                  style={{ minHeight: '44px' }}
                >
                  <Gift size={20} className="mr-2 transition-transform duration-300 group-hover:scale-105 group-focus:scale-105" />
                  <span className="transition-all duration-300">Rewards ({rewardItemCount})</span>
                </Link>
              )}
            </div>
          </nav>
        </motion.div>
      )}

      {/* Login Popup */}
      <NoSSR>
        <LoginPopup open={isLoginPopupOpen} onOpenChange={setIsLoginPopupOpen} />
      </NoSSR>

      {/* Global Achievement Notifications */}
      <AnimatedAchievementNotification
        achievement={activeAchievement}
        onClose={closeAchievement}
        showConfetti={true}
        position="top-right"
      />
    </header>
  )
}

export default Header